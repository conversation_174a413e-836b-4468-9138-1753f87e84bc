{% extends "admin/admin_layout.html" %}

{% block title %}Email Templates - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>Email Templates</h2>
            
            <div class="alert alert-info">
                <strong>Debug Info:</strong>
                <br>Templates count: {{ templates|length if templates else 'None' }}
                <br>Templates type: {{ templates.__class__.__name__ if templates else 'None' }}
            </div>
            
            <div class="mb-3">
                <a href="{{ url_for('admin.email_template_create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New Template
                </a>
                <a href="{{ url_for('admin.email_init_templates') }}" class="btn btn-warning">
                    <i class="fas fa-sync me-2"></i>Initialize Templates
                </a>
                <a href="{{ url_for('admin.email_debug') }}" class="btn btn-outline-info" target="_blank">
                    <i class="fas fa-bug me-2"></i>Debug JSON
                </a>
            </div>
            
            {% if templates and templates|length > 0 %}
            <div class="card">
                <div class="card-header">
                    <h5>Found {{ templates|length }} Templates</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Key</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Languages</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for template in templates %}
                                <tr>
                                    <td>
                                        <strong>{{ template.name }}</strong>
                                        {% if template.description %}
                                        <br><small class="text-muted">{{ template.description }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code>{{ template.template_key }}</code>
                                        {% if template.is_system_template %}
                                        <span class="badge bg-info ms-1">System</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ template.category }}</span>
                                    </td>
                                    <td>
                                        {% if template.is_active %}
                                        <span class="badge bg-success">Active</span>
                                        {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% for lang in template.supported_languages %}
                                        <span class="badge bg-light text-dark me-1">{{ lang.upper() }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="alert('Preview: {{ template.template_key }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="{{ url_for('admin.email_template_edit', template_id=template.id) }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if not template.is_system_template %}
                                            <button class="btn btn-outline-danger" onclick="alert('Delete: {{ template.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                    <h5>No Email Templates Found</h5>
                    <p class="text-muted">Click "Initialize Templates" to create system templates.</p>
                    <a href="{{ url_for('admin.email_init_templates') }}" class="btn btn-warning">
                        <i class="fas fa-sync me-2"></i>Initialize Templates
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
