{% extends "admin/admin_layout.html" %}

{% block title %}Email Analytics - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>{{ t('email.analytics.title') }}</h2>
                <div class="btn-group">
                    <a href="{{ url_for('admin.email_analytics', days=7) }}" class="btn btn-outline-primary {% if days == 7 %}active{% endif %}">7 Days</a>
                    <a href="{{ url_for('admin.email_analytics', days=30) }}" class="btn btn-outline-primary {% if days == 30 %}active{% endif %}">30 Days</a>
                    <a href="{{ url_for('admin.email_analytics', days=90) }}" class="btn btn-outline-primary {% if days == 90 %}active{% endif %}">90 Days</a>
                </div>
            </div>

            <!-- Overall Statistics -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ overall_stats.total_sent or 0 }}</h4>
                                    <p class="mb-0">{{ t('email.analytics.total_sent') }}</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ overall_stats.delivery_rate or 0 }}%</h4>
                                    <p class="mb-0">{{ t('email.analytics.delivery_rate') }}</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ overall_stats.open_rate or 0 }}%</h4>
                                    <p class="mb-0">{{ t('email.analytics.open_rate') }}</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-eye fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ overall_stats.click_rate or 0 }}%</h4>
                                    <p class="mb-0">{{ t('email.analytics.click_rate') }}</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-mouse-pointer fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Template Performance -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">{{ t('email.analytics.template_performance') }}</h5>
                        </div>
                        <div class="card-body">
                            {% if template_performance %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>{{ t('email.templates.name') }}</th>
                                            <th>{{ t('email.analytics.sent') }}</th>
                                            <th>{{ t('email.analytics.delivered') }}</th>
                                            <th>{{ t('email.analytics.opened') }}</th>
                                            <th>{{ t('email.analytics.clicked') }}</th>
                                            <th>{{ t('email.analytics.open_rate') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for template in template_performance %}
                                        <tr>
                                            <td>
                                                <strong>{{ template.template_name }}</strong>
                                                <br><small class="text-muted">{{ template.template_key }}</small>
                                            </td>
                                            <td>{{ template.total_sent }}</td>
                                            <td>{{ template.delivered }}</td>
                                            <td>{{ template.opened }}</td>
                                            <td>{{ template.clicked }}</td>
                                            <td>
                                                <span class="badge {% if template.open_rate >= 20 %}bg-success{% elif template.open_rate >= 10 %}bg-warning{% else %}bg-danger{% endif %}">
                                                    {{ template.open_rate }}%
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h5>{{ t('email.analytics.no_data') }}</h5>
                                <p class="text-muted">{{ t('email.analytics.no_data_desc') }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Queue Status -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">{{ t('email.analytics.queue_status') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>{{ t('email.analytics.queue_size') }}</span>
                                    <strong>{{ queue_stats.queue_size or 0 }}</strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>{{ t('email.analytics.worker_status') }}</span>
                                    <span class="badge {% if queue_stats.worker_running %}bg-success{% else %}bg-danger{% endif %}">
                                        {% if queue_stats.worker_running %}{{ t('common.running') }}{% else %}{{ t('common.stopped') }}{% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>{{ t('email.analytics.max_retries') }}</span>
                                    <strong>{{ queue_stats.max_retries or 0 }}</strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>{{ t('email.analytics.retry_delay') }}</span>
                                    <strong>{{ queue_stats.retry_delay or 0 }}s</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">{{ t('email.analytics.quick_actions') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('admin.email_test') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-vial me-2"></i>{{ t('email.analytics.test_email') }}
                                </a>
                                <a href="{{ url_for('admin.email_templates') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-envelope me-2"></i>{{ t('email.analytics.manage_templates') }}
                                </a>
                                <button class="btn btn-outline-info" onclick="refreshStats()">
                                    <i class="fas fa-sync-alt me-2"></i>{{ t('email.analytics.refresh') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daily Statistics Chart -->
            {% if daily_stats %}
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">{{ t('email.analytics.daily_stats') }}</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyStatsChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if daily_stats %}
// Daily Statistics Chart
const dailyStatsData = {{ daily_stats | tojson }};
const ctx = document.getElementById('dailyStatsChart').getContext('2d');

new Chart(ctx, {
    type: 'line',
    data: {
        labels: dailyStatsData.map(d => d.date),
        datasets: [
            {
                label: '{{ t("email.analytics.sent") }}',
                data: dailyStatsData.map(d => d.total_sent),
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.1
            },
            {
                label: '{{ t("email.analytics.delivered") }}',
                data: dailyStatsData.map(d => d.delivered),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            },
            {
                label: '{{ t("email.analytics.opened") }}',
                data: dailyStatsData.map(d => d.opened),
                borderColor: 'rgb(255, 206, 86)',
                backgroundColor: 'rgba(255, 206, 86, 0.1)',
                tension: 0.1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: '{{ t("email.analytics.daily_email_stats") }}'
            }
        }
    }
});
{% endif %}

function refreshStats() {
    location.reload();
}
</script>
{% endblock %}
