{% extends "admin/admin_layout.html" %}

{% block title %}
{% if template %}Edit Email Template{% else %}Create Email Template{% endif %} - Rominext
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    {% if template %}
                    {{ t('email.templates.edit_title') }}
                    {% else %}
                    {{ t('email.templates.create_title') }}
                    {% endif %}
                </h2>
                <a href="{{ url_for('admin.email_templates') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{{ t('common.back') }}
                </a>
            </div>

            <form id="templateForm" method="POST">
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Basic Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">{{ t('email.templates.basic_info') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="template_key" class="form-label">{{ t('email.templates.key') }} *</label>
                                        <input type="text" class="form-control" id="template_key" name="template_key" 
                                               value="{{ template.template_key if template else '' }}" 
                                               {% if template %}readonly{% endif %} required>
                                        <div class="form-text">{{ t('email.templates.key_help') }}</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">{{ t('email.templates.name') }} *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="{{ template.name if template else '' }}" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="category" class="form-label">{{ t('email.templates.category') }} *</label>
                                        <select class="form-select" id="category" name="category" required>
                                            <option value="">{{ t('common.select') }}</option>
                                            <option value="auth" {% if template and template.category == 'auth' %}selected{% endif %}>{{ t('email.categories.auth') }}</option>
                                            <option value="notification" {% if template and template.category == 'notification' %}selected{% endif %}>{{ t('email.categories.notification') }}</option>
                                            <option value="marketing" {% if template and template.category == 'marketing' %}selected{% endif %}>{{ t('email.categories.marketing') }}</option>
                                            <option value="system" {% if template and template.category == 'system' %}selected{% endif %}>{{ t('email.categories.system') }}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="default_language" class="form-label">{{ t('email.templates.default_language') }}</label>
                                        <select class="form-select" id="default_language" name="default_language">
                                            <option value="en" {% if not template or template.default_language == 'en' %}selected{% endif %}>English</option>
                                            <option value="fa" {% if template and template.default_language == 'fa' %}selected{% endif %}>فارسی</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="description" class="form-label">{{ t('email.templates.description') }}</label>
                                    <textarea class="form-control" id="description" name="description" rows="2">{{ template.description if template else '' }}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="variables" class="form-label">{{ t('email.templates.variables') }}</label>
                                    <input type="text" class="form-control" id="variables" name="variables" 
                                           value="{{ template.variables|join(', ') if template and template.variables else '' }}"
                                           placeholder="user_name, site_name, reset_link">
                                    <div class="form-text">{{ t('email.templates.variables_help') }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Language Tabs -->
                        <div class="card">
                            <div class="card-header">
                                <ul class="nav nav-tabs card-header-tabs" id="languageTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="en-tab" data-bs-toggle="tab" data-bs-target="#en-content" type="button" role="tab">
                                            English
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="fa-tab" data-bs-toggle="tab" data-bs-target="#fa-content" type="button" role="tab">
                                            فارسی
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content" id="languageTabContent">
                                    <!-- English Content -->
                                    <div class="tab-pane fade show active" id="en-content" role="tabpanel">
                                        <div class="mb-3">
                                            <label for="subject_en" class="form-label">{{ t('email.templates.subject') }} (English) *</label>
                                            <input type="text" class="form-control" id="subject_en" name="subjects[en]" 
                                                   value="{{ template.subjects.en if template and template.subjects else '' }}" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="html_content_en" class="form-label">{{ t('email.templates.html_content') }} (English) *</label>
                                            <textarea class="form-control" id="html_content_en" name="html_content[en]" rows="15" required>{{ template.html_content.en if template and template.html_content else '' }}</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="text_content_en" class="form-label">{{ t('email.templates.text_content') }} (English)</label>
                                            <textarea class="form-control" id="text_content_en" name="text_content[en]" rows="8">{{ template.text_content.en if template and template.text_content else '' }}</textarea>
                                        </div>
                                    </div>

                                    <!-- Persian Content -->
                                    <div class="tab-pane fade" id="fa-content" role="tabpanel">
                                        <div class="mb-3">
                                            <label for="subject_fa" class="form-label">{{ t('email.templates.subject') }} (فارسی) *</label>
                                            <input type="text" class="form-control" id="subject_fa" name="subjects[fa]" 
                                                   value="{{ template.subjects.fa if template and template.subjects else '' }}" required dir="rtl">
                                        </div>
                                        <div class="mb-3">
                                            <label for="html_content_fa" class="form-label">{{ t('email.templates.html_content') }} (فارسی) *</label>
                                            <textarea class="form-control" id="html_content_fa" name="html_content[fa]" rows="15" required dir="rtl">{{ template.html_content.fa if template and template.html_content else '' }}</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="text_content_fa" class="form-label">{{ t('email.templates.text_content') }} (فارسی)</label>
                                            <textarea class="form-control" id="text_content_fa" name="text_content[fa]" rows="8" dir="rtl">{{ template.text_content.fa if template and template.text_content else '' }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Actions -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">{{ t('common.actions') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {% if template %}{{ t('common.update') }}{% else %}{{ t('common.create') }}{% endif %}
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="previewTemplate()">
                                        <i class="fas fa-eye me-2"></i>{{ t('email.templates.preview') }}
                                    </button>
                                    {% if template %}
                                    <button type="button" class="btn btn-outline-info" onclick="duplicateTemplate()">
                                        <i class="fas fa-copy me-2"></i>{{ t('email.templates.duplicate') }}
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Template Variables Help -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">{{ t('email.templates.variables_help_title') }}</h5>
                            </div>
                            <div class="card-body">
                                <p class="small">{{ t('email.templates.variables_help_desc') }}</p>
                                <div class="mb-2">
                                    <strong>{{ t('email.templates.common_variables') }}:</strong>
                                </div>
                                <ul class="small">
                                    <li><code>{{'{{'}}user_name{{'}}'}}</code> - {{ t('email.variables.user_name') }}</li>
                                    <li><code>{{'{{'}}site_name{{'}}'}}</code> - {{ t('email.variables.site_name') }}</li>
                                    <li><code>{{'{{'}}current_year{{'}}'}}</code> - {{ t('email.variables.current_year') }}</li>
                                    <li><code>{{'{{'}}unsubscribe_url{{'}}'}}</code> - {{ t('email.variables.unsubscribe_url') }}</li>
                                </ul>
                                <div class="mb-2">
                                    <strong>{{ t('email.templates.auth_variables') }}:</strong>
                                </div>
                                <ul class="small">
                                    <li><code>{{'{{'}}reset_link{{'}}'}}</code> - {{ t('email.variables.reset_link') }}</li>
                                    <li><code>{{'{{'}}verification_link{{'}}'}}</code> - {{ t('email.variables.verification_link') }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.getElementById('templateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {};
    
    // Convert FormData to object
    for (let [key, value] of formData.entries()) {
        if (key.includes('[') && key.includes(']')) {
            // Handle nested objects like subjects[en]
            const match = key.match(/(\w+)\[(\w+)\]/);
            if (match) {
                const [, parentKey, childKey] = match;
                if (!data[parentKey]) data[parentKey] = {};
                data[parentKey][childKey] = value;
            }
        } else {
            data[key] = value;
        }
    }
    
    // Convert variables string to array
    if (data.variables) {
        data.variables = data.variables.split(',').map(v => v.trim()).filter(v => v);
    }
    
    // Submit form
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = "{{ url_for('admin.email_templates') }}";
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to save template');
    });
});

function previewTemplate() {
    // Get current form data and show preview
    const subjects = {
        en: document.getElementById('subject_en').value,
        fa: document.getElementById('subject_fa').value
    };
    const htmlContent = {
        en: document.getElementById('html_content_en').value,
        fa: document.getElementById('html_content_fa').value
    };
    
    // Simple preview - you can enhance this
    const language = document.getElementById('default_language').value;
    const subject = subjects[language] || subjects.en;
    const content = htmlContent[language] || htmlContent.en;
    
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <html>
        <head>
            <title>Email Preview</title>
            <style>body { font-family: Arial, sans-serif; margin: 20px; }</style>
        </head>
        <body>
            <h3>Subject: ${subject}</h3>
            <hr>
            <div>${content}</div>
        </body>
        </html>
    `);
}

{% if template %}
function duplicateTemplate() {
    const newKey = prompt('Enter new template key:', '{{ template.template_key }}_copy');
    const newName = prompt('Enter new template name:', '{{ template.name }} (Copy)');
    
    if (newKey && newName) {
        fetch("{{ url_for('email.duplicate_template', template_id=template.id) }}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                new_template_key: newKey,
                new_name: newName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = "{{ url_for('admin.email_templates') }}";
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to duplicate template');
        });
    }
}
{% endif %}
</script>
{% endblock %}
