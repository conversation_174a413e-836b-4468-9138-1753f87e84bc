{% extends "admin/admin_layout.html" %}

{% block title %}Test Email - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>{{ t('email.test.title') }}</h2>
                <a href="{{ url_for('admin.email_analytics') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{{ t('common.back') }}
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- SMTP Connection Test -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">{{ t('email.test.smtp_test') }}</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">{{ t('email.test.smtp_test_desc') }}</p>
                            <button type="button" class="btn btn-primary" onclick="testSMTP()">
                                <i class="fas fa-plug me-2"></i>{{ t('email.test.test_connection') }}
                            </button>
                            <div id="smtpResult" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Send Test Email -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">{{ t('email.test.send_test') }}</h5>
                        </div>
                        <div class="card-body">
                            <form id="testEmailForm">
                                <div class="mb-3">
                                    <label for="to_email" class="form-label">{{ t('email.test.recipient') }} *</label>
                                    <input type="email" class="form-control" id="to_email" name="to_email" 
                                           value="{{ current_user.email }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="subject" class="form-label">{{ t('email.test.subject') }}</label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           value="Test Email from Rominext">
                                </div>
                                <div class="mb-3">
                                    <label for="html_content" class="form-label">{{ t('email.test.html_content') }}</label>
                                    <textarea class="form-control" id="html_content" name="html_content" rows="8">
<h1>Test Email</h1>
<p>Hello!</p>
<p>This is a test email from <strong>Rominext</strong> email system.</p>
<p>If you received this email, the email system is working correctly.</p>
<p>Best regards,<br>The Rominext Team</p>
                                    </textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="text_content" class="form-label">{{ t('email.test.text_content') }}</label>
                                    <textarea class="form-control" id="text_content" name="text_content" rows="6">
Test Email

Hello!

This is a test email from Rominext email system.

If you received this email, the email system is working correctly.

Best regards,
The Rominext Team
                                    </textarea>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-paper-plane me-2"></i>{{ t('email.test.send_email') }}
                                </button>
                            </form>
                            <div id="testEmailResult" class="mt-3"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Current SMTP Settings -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">{{ t('email.test.current_settings') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>{{ t('email.test.smtp_host') }}:</strong>
                                <span class="text-muted">{{ config.SMTP_HOST or 'Not configured' }}</span>
                            </div>
                            <div class="mb-2">
                                <strong>{{ t('email.test.smtp_port') }}:</strong>
                                <span class="text-muted">{{ config.SMTP_PORT or 'Not configured' }}</span>
                            </div>
                            <div class="mb-2">
                                <strong>{{ t('email.test.smtp_username') }}:</strong>
                                <span class="text-muted">{{ config.SMTP_USERNAME or 'Not configured' }}</span>
                            </div>
                            <div class="mb-2">
                                <strong>{{ t('email.test.use_tls') }}:</strong>
                                <span class="badge {% if config.SMTP_USE_TLS %}bg-success{% else %}bg-secondary{% endif %}">
                                    {{ 'Yes' if config.SMTP_USE_TLS else 'No' }}
                                </span>
                            </div>
                            <div class="mb-2">
                                <strong>{{ t('email.test.use_ssl') }}:</strong>
                                <span class="badge {% if config.SMTP_USE_SSL %}bg-success{% else %}bg-secondary{% endif %}">
                                    {{ 'Yes' if config.SMTP_USE_SSL else 'No' }}
                                </span>
                            </div>
                            <div class="mb-2">
                                <strong>{{ t('email.test.from_email') }}:</strong>
                                <span class="text-muted">{{ config.EMAIL_FROM_ADDRESS or 'Not configured' }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Template Test -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">{{ t('email.test.template_test') }}</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted small">{{ t('email.test.template_test_desc') }}</p>
                            <div class="mb-3">
                                <label for="template_key" class="form-label">{{ t('email.test.template') }}</label>
                                <select class="form-select" id="template_key">
                                    <option value="">{{ t('common.select') }}</option>
                                    <option value="welcome">Welcome Email</option>
                                    <option value="password_reset">Password Reset</option>
                                    <option value="email_verification">Email Verification</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="template_language" class="form-label">{{ t('email.test.language') }}</label>
                                <select class="form-select" id="template_language">
                                    <option value="en">English</option>
                                    <option value="fa">فارسی</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-outline-primary" onclick="testTemplate()">
                                <i class="fas fa-envelope me-2"></i>{{ t('email.test.send_template') }}
                            </button>
                            <div id="templateTestResult" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Quick Test -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">{{ t('email.test.quick_actions') }}</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted small">{{ t('email.test.quick_test_desc') }}</p>
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('admin.email_quick_send_test') }}" class="btn btn-success">
                                    <i class="fas fa-paper-plane me-2"></i>Send Quick Test Email
                                </a>
                                <a href="{{ url_for('admin.email_templates') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-envelope me-2"></i>{{ t('email.test.manage_templates') }}
                                </a>
                                <a href="{{ url_for('admin.email_analytics') }}" class="btn btn-outline-info">
                                    <i class="fas fa-chart-line me-2"></i>{{ t('email.test.view_analytics') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testSMTP() {
    const resultDiv = document.getElementById('smtpResult');
    resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing SMTP connection...';
    
    fetch('{{ url_for("admin.email_test") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ test_type: 'smtp' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>' + data.message + '</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>Error: ' + data.error + '</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>Failed to test SMTP connection</div>';
    });
}

document.getElementById('testEmailForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    const resultDiv = document.getElementById('testEmailResult');
    resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Sending test email...';
    
    fetch('{{ url_for("admin.email_test") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>Test email sent successfully!</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>Error: ' + data.error + '</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>Failed to send test email</div>';
    });
});

function testTemplate() {
    const templateKey = document.getElementById('template_key').value;
    const language = document.getElementById('template_language').value;
    
    if (!templateKey) {
        alert('Please select a template');
        return;
    }
    
    const resultDiv = document.getElementById('templateTestResult');
    resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Sending template email...';
    
    fetch('{{ url_for("admin.email_api_send") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            template_key: templateKey,
            to_email: '{{ current_user.email }}',
            language: language,
            variables: {
                user_name: '{{ current_user.name or current_user.email }}',
                reset_link: 'https://example.com/reset',
                verification_link: 'https://example.com/verify'
            }
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>Template email queued successfully!</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>Error: ' + data.error + '</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>Failed to send template email</div>';
    });
}
</script>
{% endblock %}
