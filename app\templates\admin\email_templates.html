{% extends "admin/admin_layout.html" %}

{% block title %}Email Templates - Rominext{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Email Templates</h2>
                <div class="btn-group">
                    <a href="{{ url_for('admin.email_template_create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create New Template
                    </a>
                    <a href="{{ url_for('admin.email_init_templates') }}" class="btn btn-warning">
                        <i class="fas fa-sync me-2"></i>Initialize Templates
                    </a>
                    <a href="{{ url_for('admin.email_debug') }}" class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-bug me-2"></i>Debug
                    </a>
                </div>
            </div>

            <!-- Templates List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Email Templates List</h5>
                    <!-- Debug Info -->
                    <small class="text-muted">
                        Debug: templates = {{ templates|length if templates else 'None' }} items
                    </small>
                </div>
                <div class="card-body">
                    <!-- Force show templates for debugging -->
                    <div class="alert alert-info">
                        <strong>Debug:</strong> templates = {{ templates }}<br>
                        <strong>Length:</strong> {{ templates|length if templates else 'None' }}<br>
                        <strong>Type:</strong> {{ templates.__class__.__name__ if templates else 'None' }}
                    </div>

                    {% if templates %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Key</th>
                                    <th>Category</th>
                                    <th>Languages</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for template in templates %}
                                <tr>
                                    <td>
                                        <strong>{{ template.name }}</strong>
                                        {% if template.description %}
                                        <br><small class="text-muted">{{ template.description }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code>{{ template.template_key }}</code>
                                        {% if template.is_system_template %}
                                        <span class="badge bg-info ms-1">System</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ template.category }}</span>
                                    </td>
                                    <td>
                                        {% for lang in template.supported_languages %}
                                        <span class="badge bg-light text-dark me-1">{{ lang.upper() }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% if template.is_active %}
                                        <span class="badge bg-success">Active</span>
                                        {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ template.created_at.strftime('%Y-%m-%d %H:%M') if template.created_at else '-' }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="previewTemplate('{{ template.id }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="{{ url_for('admin.email_template_edit', template_id=template.id) }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if not template.is_system_template %}
                                            <button class="btn btn-outline-danger" onclick="deleteTemplate('{{ template.id }}', '{{ template.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                        <h5>No Email Templates Found</h5>
                        <p class="text-muted">Templates variable is empty or None.</p>
                        <div class="btn-group">
                            <a href="{{ url_for('admin.email_init_templates') }}" class="btn btn-warning">
                                <i class="fas fa-sync me-2"></i>Initialize Templates
                            </a>
                            <a href="{{ url_for('admin.email_template_create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Template
                            </a>
                            <a href="{{ url_for('admin.email_debug') }}" class="btn btn-outline-info" target="_blank">
                                <i class="fas fa-bug me-2"></i>Debug
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('email.templates.preview') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">{{ t('email.templates.language') }}</label>
                    <select class="form-select" id="previewLanguage" onchange="updatePreview()">
                        <option value="en">English</option>
                        <option value="fa">فارسی</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">{{ t('email.templates.subject') }}</label>
                    <div class="form-control" id="previewSubject" style="background-color: #f8f9fa;"></div>
                </div>
                <div class="mb-3">
                    <label class="form-label">{{ t('email.templates.content') }}</label>
                    <div class="border rounded p-3" id="previewContent" style="min-height: 300px; background-color: white;"></div>
                </div>
                <div class="mb-3">
                    <label class="form-label">{{ t('email.templates.variables') }}</label>
                    <div id="previewVariables"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('common.close') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('email.templates.delete_confirm') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>{{ t('email.templates.delete_warning') }}</p>
                <p><strong id="deleteTemplateName"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('common.cancel') }}</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">{{ t('common.delete') }}</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentTemplateId = null;

function previewTemplate(templateId) {
    currentTemplateId = templateId;
    updatePreview();
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

function updatePreview() {
    if (!currentTemplateId) return;
    
    const language = document.getElementById('previewLanguage').value;
    
    fetch(`{{ url_for('admin.email_template_preview', template_id='TEMPLATE_ID') }}`.replace('TEMPLATE_ID', currentTemplateId) + `?language=${language}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error: ' + data.error);
                return;
            }
            
            document.getElementById('previewSubject').textContent = data.subject;
            document.getElementById('previewContent').innerHTML = data.html_content;
            
            // Show variables
            const variablesDiv = document.getElementById('previewVariables');
            if (data.variables && data.variables.length > 0) {
                variablesDiv.innerHTML = data.variables.map(v => `<span class="badge bg-info me-1">${v}</span>`).join('');
            } else {
                variablesDiv.innerHTML = '<span class="text-muted">{{ t("email.templates.no_variables") }}</span>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to load preview');
        });
}

function deleteTemplate(templateId, templateName) {
    document.getElementById('deleteTemplateName').textContent = templateName;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
    
    document.getElementById('confirmDelete').onclick = function() {
        fetch(`{{ url_for('admin.email_template_delete', template_id='TEMPLATE_ID') }}`.replace('TEMPLATE_ID', templateId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete template');
        });
        
        modal.hide();
    };
}
</script>
{% endblock %}
